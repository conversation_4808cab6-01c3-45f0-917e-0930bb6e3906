
<svg width="980" height="670" viewBox="0 0 980 670" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="980" height="670" fill="#FFFFFF"/>
  <!-- Device <PERSON> -->
  <rect x="50" y="60" width="880" height="490" fill="none" stroke="#28a745" stroke-width="3" stroke-dasharray="10,5" rx="15"/>
  <text x="70" y="90" font-size="22" font-weight="bold" fill="#28a745">DEVICE SECURITY BOUNDARY</text>
  <!-- User Interface Layer -->
  <rect x="70" y="110" width="840" height="80" fill="#61dafb" fill-opacity="0.2" stroke="#0056b3" stroke-width="2" rx="10"/>
  <text x="490" y="140" text-anchor="middle" font-size="20" font-weight="bold">React Native Application</text>
  <text x="490" y="165" text-anchor="middle" font-size="18">Chat Interface • Model Management • Performance Monitoring</text>
  <!-- Processing Layer -->
  <rect x="70" y="220" width="360" height="200" fill="#ff6b6b" fill-opacity="0.15" stroke="#dc3545" stroke-width="2" rx="10"/>
  <text x="260" y="250" text-anchor="middle" font-size="20" font-weight="bold">LLM Inference</text>
  <!-- LLM Components -->
  <rect x="90" y="340" width="150" height="60" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="165" y="367" text-anchor="middle" font-size="16" font-weight="bold">LLaMA.rn</text>
  <text x="165" y="383" text-anchor="middle" font-size="14">GGUF Models</text>
  <rect x="260" y="340" width="150" height="60" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="335" y="367" text-anchor="middle" font-size="16" font-weight="bold">Context Engine</text>
  <text x="335" y="383" text-anchor="middle" font-size="14">RAG Integration</text>
  <rect x="90" y="270" width="320" height="60" fill="#ff6b6b" fill-opacity="0.25" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="250" y="295" text-anchor="middle" font-size="16" font-weight="bold">Generation Pipeline</text>
  <text x="250" y="315" text-anchor="middle" font-size="14">Token Streaming • Performance Metrics</text>
  <!-- RAG Processing Layer -->
  <rect x="530" y="220" width="380" height="200" fill="#4ecdc4" fill-opacity="0.15" stroke="#17a2b8" stroke-width="2" rx="10"/>
  <text x="720" y="250" text-anchor="middle" font-size="20" font-weight="bold">RAG Pipeline</text>
  <!-- RAG Components -->
  <rect x="550" y="340" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="600" y="367" text-anchor="middle" font-size="16" font-weight="bold">ONNX</text>
  <text x="600" y="383" text-anchor="middle" font-size="14">Embeddings</text>
  <rect x="670" y="340" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="720" y="367" text-anchor="middle" font-size="16" font-weight="bold">Tokenizer</text>
  <text x="720" y="383" text-anchor="middle" font-size="14">BERT-style</text>
  <rect x="790" y="340" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="840" y="367" text-anchor="middle" font-size="16" font-weight="bold">HNSW</text>
  <text x="840" y="383" text-anchor="middle" font-size="14">Vector Search</text>
  <rect x="550" y="270" width="340" height="60" fill="#4ecdc4" fill-opacity="0.25" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="720" y="295" text-anchor="middle" font-size="16" font-weight="bold">Document Retrieval</text>
  <text x="720" y="315" text-anchor="middle" font-size="14">Chunked Metadata • LRU Cache • Relevance Ranking</text>
  <!-- Storage Layer -->
  <rect x="70" y="450" width="610" height="80" fill="#ffd93d" fill-opacity="0.2" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="375" y="480" text-anchor="middle" font-size="20" font-weight="bold">Local Storage</text>
  <text x="375" y="505" text-anchor="middle" font-size="18">HNSW Index • Document Chunks • AI Models • Runtime Cache</text>
  <!-- Native Bridge -->
  <rect x="740" y="450" width="170" height="80" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="825" y="480" text-anchor="middle" font-size="20" font-weight="bold">Native Bridge</text>
  <text x="825" y="505" text-anchor="middle" font-size="18">Rust HNSW</text>
  <!-- Data Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- Main Data Flow -->
  <!-- User Input -->
  <path d="M 215 190 L 215 220" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="90" y="210" font-size="18" font-weight="bold">1. User Query</text>
  <!-- Query to RAG -->
  <path d="M 430 300 L 530 300" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="440" y="285" font-size="18" font-weight="bold">2. Process</text>
  <!-- Context Back -->
  <path d="M 530 350 L 430 350" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="440" y="375" font-size="18" font-weight="bold">3. Context</text>
  <!-- Storage Access -->
  <path d="M 600 420 L 600 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 250 420 L 250 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 825 420 L 825 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 740 490 L 680 490" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Response Flow -->
  <path d="M 305 220 L 305 190" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="320" y="210" font-size="18" font-weight="bold">4. Response</text>
  <!-- Key Benefits -->
  <rect x="415" y="570" width="150" height="50" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="5"/>
  <text x="490" y="600" text-anchor="middle" font-size="20" font-weight="bold">Internet</text>
</svg>